2025-06-11 00:06:16,712 WARN c.z.h.p.<PERSON>kari<PERSON>ool$HouseKeeper [HikariPool-1 housekeeper] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=6h41m22s728ms563µs100ns).
2025-06-11 01:19:52,833 WARN c.m.s.j.SQLServerConnection [HikariPool-1 connection adder] ConnectionID:94 ClientConnectionId: f8eb25bc-1d56-459c-8455-33d7a9392f1d Prelogin error: host pilotodbqa.database.windows.net port 1433 Error reading prelogin response: Connection reset ClientConnectionId:f8eb25bc-1d56-459c-8455-33d7a9392f1d
2025-06-11 09:37:47,531 WARN c.z.h.p.HikariPool$HouseKeeper [HikariPool-1 housekeeper] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=9h8m32s258ms772µs700ns).
2025-06-11 10:05:32,057 WARN c.z.h.p.HikariPool$HouseKeeper [HikariPool-1 housekeeper] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m14s104ms789µs200ns).
2025-06-11 10:28:48,564 INFO o.s.o.j.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:28:48,706 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown initiated...
2025-06-11 10:28:48,750 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown completed.
2025-06-11 10:29:05,267 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 5140 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-11 10:29:05,275 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-11 10:29:16,843 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 10:29:19,435 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 2576 ms. Found 297 JPA repository interfaces.
2025-06-11 10:29:21,810 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8095 (http)
2025-06-11 10:29:21,827 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8095"]
2025-06-11 10:29:21,838 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-11 10:29:21,838 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-11 10:29:21,974 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-11 10:29:21,974 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 16528 ms
2025-06-11 10:29:24,105 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-11 10:29:27,516 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: 5a79bbef-cd60-4b36-8d11-de2d91f0b137
2025-06-11 10:29:27,523 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-11 10:29:27,604 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 10:29:27,784 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-11 10:29:27,891 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-11 10:29:28,500 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-11 10:29:31,454 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-11 10:29:31,454 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-11 10:29:36,490 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-11 10:29:36,557 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:29:37,739 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-11 10:30:02,575 WARN o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration [main] spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 10:30:06,366 INFO o.s.b.a.e.w.EndpointLinksResolver [main] Exposing 1 endpoint beneath base path '/actuator'
2025-06-11 10:30:06,510 INFO o.a.j.l.DirectJDKLog [main] Starting ProtocolHandler ["http-nio-8095"]
2025-06-11 10:30:06,547 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat started on port 8095 (http) with context path '/'
2025-06-11 10:30:06,572 INFO o.s.b.StartupInfoLogger [main] Started BusinessApplication in 63.589 seconds (process running for 66.267)
2025-06-11 10:30:56,260 INFO o.a.j.l.DirectJDKLog [http-nio-8095-exec-1] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 10:30:56,262 INFO o.s.w.s.FrameworkServlet [http-nio-8095-exec-1] Initializing Servlet 'dispatcherServlet'
2025-06-11 10:30:56,264 INFO o.s.w.s.FrameworkServlet [http-nio-8095-exec-1] Completed initialization in 2 ms
2025-06-11 10:30:56,426 INFO c.m.s.s.c.ConfirmMoveInstructionController [http-nio-8095-exec-1] Received confirm movement instruction request: ConfirmMoveInstructionInput.Root(prefix=ConfirmMoveInstructionInput.Prefix(input=ConfirmMoveInstructionInput.Input(moveInstructionId=1812, userAlias=<EMAIL>)))
2025-06-11 10:30:56,920 INFO c.m.s.s.s.ConfirmMoveInstructionService [http-nio-8095-exec-1] Starting confirm move instruction for ID: 1812 by user: <EMAIL>
2025-06-11 10:30:57,710 WARN o.h.e.j.s.SqlExceptionHelper [http-nio-8095-exec-1] SQL Error: 207, SQLState: S0001
2025-06-11 10:30:57,712 ERROR o.h.e.j.s.SqlExceptionHelper [http-nio-8095-exec-1] Invalid column name 'correo_enviado_restaurar'.
2025-06-11 10:30:57,955 ERROR c.m.s.s.s.ConfirmMoveInstructionService [http-nio-8095-exec-1] Error confirming movement instruction
org.springframework.dao.InvalidDataAccessResourceUsageException: JDBC exception executing SQL [select u1_0.usuario_id,u1_0.contador_login,u1_0.id,u1_0.adjunto_foto_id,c1_0.empresa_id,c1_0.abreviatura,c1_0.direccion,c1_0.unidad_negocio_id,c1_0.cat_empresa_origen_creacion,c1_0.tipo_documento,c1_0.razon_comercial,c1_0.empresa_alias,c1_0.linea_credito_actual,c1_0.documento,c1_0.latitud,c1_0.razon_social,c1_0.longitud,c1_0.correo,c1_0.fecha_modificacion,c1_0.usuario_modificacion_id,c1_0.telefono,c1_0.tipo_regimen,c1_0.fecha_registro,c1_0.usuario_registro_id,c1_0.estado,c1_0.suspendido,c1_0.tipo_contribuyente,u1_0.fecha_cambio_clave,u1_0.apellido_paterno,u1_0.clave,u1_0.correo,u1_0.fecha_modificacion,u1_0.usuario_modificacion_id,u1_0.nombres,u1_0.persona_id,u1_0.fecha_registro,u1_0.usuario_registro_id,u1_0.correo_enviado_restaurar,u1_0.apellido_materno,u1_0.estado from seg.usuario u1_0 left join ges.empresa c1_0 on c1_0.empresa_id=u1_0.empresa_id where u1_0.id=?] [Invalid column name 'correo_enviado_restaurar'.] [n/a]; SQL [n/a]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:136)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy212.findByAlias(Unknown Source)
	at com.maersk.sd1.sdy.service.ConfirmMoveInstructionService.confirmMoveInstruction(ConfirmMoveInstructionService.java:60)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:379)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.maersk.sd1.sdy.service.ConfirmMoveInstructionService$$SpringCGLIB$$0.confirmMoveInstruction(<generated>)
	at com.maersk.sd1.sdy.controller.ConfirmMoveInstructionController.confirmMoveInstruction(ConfirmMoveInstructionController.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:384)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1575)
Caused by: org.hibernate.exception.SQLGrammarException: JDBC exception executing SQL [select u1_0.usuario_id,u1_0.contador_login,u1_0.id,u1_0.adjunto_foto_id,c1_0.empresa_id,c1_0.abreviatura,c1_0.direccion,c1_0.unidad_negocio_id,c1_0.cat_empresa_origen_creacion,c1_0.tipo_documento,c1_0.razon_comercial,c1_0.empresa_alias,c1_0.linea_credito_actual,c1_0.documento,c1_0.latitud,c1_0.razon_social,c1_0.longitud,c1_0.correo,c1_0.fecha_modificacion,c1_0.usuario_modificacion_id,c1_0.telefono,c1_0.tipo_regimen,c1_0.fecha_registro,c1_0.usuario_registro_id,c1_0.estado,c1_0.suspendido,c1_0.tipo_contribuyente,u1_0.fecha_cambio_clave,u1_0.apellido_paterno,u1_0.clave,u1_0.correo,u1_0.fecha_modificacion,u1_0.usuario_modificacion_id,u1_0.nombres,u1_0.persona_id,u1_0.fecha_registro,u1_0.usuario_registro_id,u1_0.correo_enviado_restaurar,u1_0.apellido_materno,u1_0.estado from seg.usuario u1_0 left join ges.empresa c1_0 on c1_0.empresa_id=u1_0.empresa_id where u1_0.id=?] [Invalid column name 'correo_enviado_restaurar'.] [n/a]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:91)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:94)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:258)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:164)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.advanceNext(JdbcValuesResultSetImpl.java:218)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.processNext(JdbcValuesResultSetImpl.java:98)
	at org.hibernate.sql.results.jdbc.internal.AbstractJdbcValues.next(AbstractJdbcValues.java:19)
	at org.hibernate.sql.results.internal.RowProcessingStateStandardImpl.next(RowProcessingStateStandardImpl.java:66)
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:200)
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:33)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:361)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:168)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.list(JdbcSelectExecutorStandardImpl.java:93)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:31)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$0(ConcreteSqmSelectQueryPlan.java:109)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:305)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:246)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:509)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:427)
	at org.hibernate.query.spi.AbstractSelectionQuery.getSingleResult(AbstractSelectionQuery.java:559)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution$SingleEntityExecution.doExecute(JpaQueryExecution.java:223)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution.execute(JpaQueryExecution.java:92)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.doExecute(AbstractJpaQuery.java:152)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.execute(AbstractJpaQuery.java:140)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:169)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:148)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:379)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 73 common frames omitted
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: Invalid column name 'correo_enviado_restaurar'.
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:270)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1735)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatement(SQLServerPreparedStatement.java:675)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtExecCmd.doExecute(SQLServerPreparedStatement.java:594)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7745)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:4391)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:276)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:246)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeQuery(SQLServerPreparedStatement.java:509)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:240)
	... 105 common frames omitted
2025-06-11 10:30:58,706 ERROR c.m.s.s.c.ConfirmMoveInstructionController [http-nio-8095-exec-1] Error processing confirm movement instruction request
org.springframework.transaction.UnexpectedRollbackException: Transaction silently rolled back because it has been marked as rollback-only
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:804)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:758)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:663)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:413)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.maersk.sd1.sdy.service.ConfirmMoveInstructionService$$SpringCGLIB$$0.confirmMoveInstruction(<generated>)
	at com.maersk.sd1.sdy.controller.ConfirmMoveInstructionController.confirmMoveInstruction(ConfirmMoveInstructionController.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:384)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1575)
2025-06-11 10:37:50,494 INFO o.s.o.j.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:37:50,494 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown initiated...
2025-06-11 10:37:50,505 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown completed.
2025-06-11 10:38:03,434 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 14980 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-11 10:38:03,438 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-11 10:38:13,962 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 10:38:15,810 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 1825 ms. Found 297 JPA repository interfaces.
2025-06-11 10:38:17,468 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8095 (http)
2025-06-11 10:38:17,490 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8095"]
2025-06-11 10:38:17,497 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-11 10:38:17,499 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-11 10:38:17,604 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-11 10:38:17,604 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 14030 ms
2025-06-11 10:38:19,363 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-11 10:38:22,267 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: 30e9d898-2f14-422e-b26d-32f6c1073fce
2025-06-11 10:38:22,268 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-11 10:38:22,351 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 10:38:22,437 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-11 10:38:22,505 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-11 10:38:22,926 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-11 10:38:25,392 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-11 10:38:25,392 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-11 10:38:28,927 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-11 10:38:28,960 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:38:29,816 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-11 10:38:48,905 WARN o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration [main] spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 10:38:51,447 INFO o.s.b.a.e.w.EndpointLinksResolver [main] Exposing 1 endpoint beneath base path '/actuator'
2025-06-11 10:38:51,609 INFO o.a.j.l.DirectJDKLog [main] Starting ProtocolHandler ["http-nio-8095"]
2025-06-11 10:38:51,669 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat started on port 8095 (http) with context path '/'
2025-06-11 10:38:51,703 INFO o.s.b.StartupInfoLogger [main] Started BusinessApplication in 50.218 seconds (process running for 51.601)
2025-06-11 10:38:57,485 INFO o.a.j.l.DirectJDKLog [http-nio-8095-exec-2] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 10:38:57,485 INFO o.s.w.s.FrameworkServlet [http-nio-8095-exec-2] Initializing Servlet 'dispatcherServlet'
2025-06-11 10:38:57,494 INFO o.s.w.s.FrameworkServlet [http-nio-8095-exec-2] Completed initialization in 5 ms
2025-06-11 10:38:57,708 INFO c.m.s.s.c.ConfirmMoveInstructionController [http-nio-8095-exec-2] Received confirm movement instruction request: ConfirmMoveInstructionInput.Root(prefix=ConfirmMoveInstructionInput.Prefix(input=ConfirmMoveInstructionInput.Input(moveInstructionId=1812, userAlias=<EMAIL>)))
2025-06-11 10:38:58,178 INFO c.m.s.s.s.ConfirmMoveInstructionService [http-nio-8095-exec-2] Starting confirm move instruction for ID: 1812 by user: <EMAIL>
2025-06-11 10:38:58,575 WARN o.h.e.j.s.SqlExceptionHelper [http-nio-8095-exec-2] SQL Error: 207, SQLState: S0001
2025-06-11 10:38:58,575 ERROR o.h.e.j.s.SqlExceptionHelper [http-nio-8095-exec-2] Invalid column name 'correo_enviado_restaurar'.
2025-06-11 10:38:58,596 ERROR c.m.s.s.s.ConfirmMoveInstructionService [http-nio-8095-exec-2] Error confirming movement instruction
org.springframework.dao.InvalidDataAccessResourceUsageException: JDBC exception executing SQL [select u1_0.usuario_id,u1_0.contador_login,u1_0.id,u1_0.adjunto_foto_id,u1_0.empresa_id,u1_0.fecha_cambio_clave,u1_0.apellido_paterno,u1_0.clave,u1_0.correo,u1_0.fecha_modificacion,u1_0.usuario_modificacion_id,u1_0.nombres,u1_0.persona_id,u1_0.fecha_registro,u1_0.usuario_registro_id,u1_0.correo_enviado_restaurar,u1_0.apellido_materno,u1_0.estado from seg.usuario u1_0 where u1_0.id=?] [Invalid column name 'correo_enviado_restaurar'.] [n/a]; SQL [n/a]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:136)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy212.findByUserAlias(Unknown Source)
	at com.maersk.sd1.sdy.service.ConfirmMoveInstructionService.confirmMoveInstruction(ConfirmMoveInstructionService.java:60)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:379)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.maersk.sd1.sdy.service.ConfirmMoveInstructionService$$SpringCGLIB$$0.confirmMoveInstruction(<generated>)
	at com.maersk.sd1.sdy.controller.ConfirmMoveInstructionController.confirmMoveInstruction(ConfirmMoveInstructionController.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:384)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1575)
Caused by: org.hibernate.exception.SQLGrammarException: JDBC exception executing SQL [select u1_0.usuario_id,u1_0.contador_login,u1_0.id,u1_0.adjunto_foto_id,u1_0.empresa_id,u1_0.fecha_cambio_clave,u1_0.apellido_paterno,u1_0.clave,u1_0.correo,u1_0.fecha_modificacion,u1_0.usuario_modificacion_id,u1_0.nombres,u1_0.persona_id,u1_0.fecha_registro,u1_0.usuario_registro_id,u1_0.correo_enviado_restaurar,u1_0.apellido_materno,u1_0.estado from seg.usuario u1_0 where u1_0.id=?] [Invalid column name 'correo_enviado_restaurar'.] [n/a]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:91)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:94)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:258)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:164)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.advanceNext(JdbcValuesResultSetImpl.java:218)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.processNext(JdbcValuesResultSetImpl.java:98)
	at org.hibernate.sql.results.jdbc.internal.AbstractJdbcValues.next(AbstractJdbcValues.java:19)
	at org.hibernate.sql.results.internal.RowProcessingStateStandardImpl.next(RowProcessingStateStandardImpl.java:66)
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:200)
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:33)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:361)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:168)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.list(JdbcSelectExecutorStandardImpl.java:93)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:31)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$0(ConcreteSqmSelectQueryPlan.java:109)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:305)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:246)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:509)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:427)
	at org.hibernate.query.spi.AbstractSelectionQuery.getSingleResult(AbstractSelectionQuery.java:559)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution$SingleEntityExecution.doExecute(JpaQueryExecution.java:223)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution.execute(JpaQueryExecution.java:92)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.doExecute(AbstractJpaQuery.java:152)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.execute(AbstractJpaQuery.java:140)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:169)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:148)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:379)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 73 common frames omitted
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: Invalid column name 'correo_enviado_restaurar'.
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:270)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1735)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatement(SQLServerPreparedStatement.java:675)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtExecCmd.doExecute(SQLServerPreparedStatement.java:594)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7745)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:4391)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:276)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:246)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeQuery(SQLServerPreparedStatement.java:509)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:240)
	... 105 common frames omitted
2025-06-11 10:38:59,075 ERROR c.m.s.s.c.ConfirmMoveInstructionController [http-nio-8095-exec-2] Error processing confirm movement instruction request
org.springframework.transaction.UnexpectedRollbackException: Transaction silently rolled back because it has been marked as rollback-only
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:804)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:758)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:663)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:413)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.maersk.sd1.sdy.service.ConfirmMoveInstructionService$$SpringCGLIB$$0.confirmMoveInstruction(<generated>)
	at com.maersk.sd1.sdy.controller.ConfirmMoveInstructionController.confirmMoveInstruction(ConfirmMoveInstructionController.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:384)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1575)
2025-06-11 10:40:22,153 INFO o.s.o.j.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:40:22,153 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown initiated...
2025-06-11 10:40:22,163 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown completed.
2025-06-11 10:40:28,876 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 8152 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-11 10:40:28,885 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-11 10:40:34,271 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 10:40:36,317 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 2028 ms. Found 297 JPA repository interfaces.
2025-06-11 10:40:38,460 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8095 (http)
2025-06-11 10:40:38,486 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8095"]
2025-06-11 10:40:38,493 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-11 10:40:38,493 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-11 10:40:38,633 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-11 10:40:38,633 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 9572 ms
2025-06-11 10:40:40,432 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-11 10:40:43,938 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: 1e13a549-116f-4cba-a216-c771ef16e1c8
2025-06-11 10:40:43,943 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-11 10:40:44,017 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 10:40:44,093 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-11 10:40:44,150 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-11 10:40:44,544 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-11 10:40:47,467 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-11 10:40:47,467 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-11 10:40:50,963 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-11 10:40:51,003 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:40:51,901 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-11 10:41:16,671 WARN o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration [main] spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 10:41:19,886 INFO o.s.b.a.e.w.EndpointLinksResolver [main] Exposing 1 endpoint beneath base path '/actuator'
2025-06-11 10:41:20,042 INFO o.a.j.l.DirectJDKLog [main] Starting ProtocolHandler ["http-nio-8095"]
2025-06-11 10:41:20,085 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat started on port 8095 (http) with context path '/'
2025-06-11 10:41:20,110 INFO o.s.b.StartupInfoLogger [main] Started BusinessApplication in 52.172 seconds (process running for 53.235)
2025-06-11 10:41:24,695 INFO o.a.j.l.DirectJDKLog [http-nio-8095-exec-1] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 10:41:24,695 INFO o.s.w.s.FrameworkServlet [http-nio-8095-exec-1] Initializing Servlet 'dispatcherServlet'
2025-06-11 10:41:24,703 INFO o.s.w.s.FrameworkServlet [http-nio-8095-exec-1] Completed initialization in 8 ms
2025-06-11 10:41:24,891 INFO c.m.s.s.c.ConfirmMoveInstructionController [http-nio-8095-exec-1] Received confirm movement instruction request: ConfirmMoveInstructionInput.Root(prefix=ConfirmMoveInstructionInput.Prefix(input=ConfirmMoveInstructionInput.Input(moveInstructionId=1812, userAlias=<EMAIL>)))
2025-06-11 10:41:25,454 INFO c.m.s.s.s.ConfirmMoveInstructionService [http-nio-8095-exec-1] Starting confirm move instruction for ID: 1812 by user: <EMAIL>
2025-06-11 10:41:26,669 INFO c.m.s.s.s.ConfirmMoveInstructionService [http-nio-8095-exec-1] Confirmed status ID: 42896
2025-06-11 10:41:30,192 INFO c.m.s.s.c.ConfirmMoveInstructionController [http-nio-8095-exec-1] Movement instruction confirmation completed with status: BUSINESS_ERROR
2025-06-11 10:42:13,010 INFO o.s.o.j.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:42:13,010 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown initiated...
2025-06-11 10:42:13,020 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown completed.
2025-06-11 10:42:31,993 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 25172 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-11 10:42:31,999 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-11 10:42:38,176 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 10:42:40,498 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 2312 ms. Found 297 JPA repository interfaces.
2025-06-11 10:42:42,536 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8095 (http)
2025-06-11 10:42:42,554 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8095"]
2025-06-11 10:42:42,561 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-11 10:42:42,561 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-11 10:42:42,674 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-11 10:42:42,681 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 10553 ms
2025-06-11 10:42:44,661 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-11 10:42:47,428 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: 51d65bfa-99a6-454e-8cf6-355fb6558305
2025-06-11 10:42:47,429 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-11 10:42:47,487 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 10:42:47,570 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-11 10:42:47,618 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-11 10:42:48,006 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-11 10:42:51,292 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-11 10:42:51,300 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-11 10:42:54,923 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-11 10:42:54,954 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:42:55,869 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-11 10:43:16,903 WARN o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration [main] spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 10:43:19,799 INFO o.s.b.a.e.w.EndpointLinksResolver [main] Exposing 1 endpoint beneath base path '/actuator'
2025-06-11 10:43:19,929 INFO o.a.j.l.DirectJDKLog [main] Starting ProtocolHandler ["http-nio-8095"]
2025-06-11 10:43:20,000 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat started on port 8095 (http) with context path '/'
2025-06-11 10:43:20,024 INFO o.s.b.StartupInfoLogger [main] Started BusinessApplication in 49.207 seconds (process running for 50.5)
2025-06-11 10:43:22,832 INFO o.a.j.l.DirectJDKLog [http-nio-8095-exec-1] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 10:43:22,834 INFO o.s.w.s.FrameworkServlet [http-nio-8095-exec-1] Initializing Servlet 'dispatcherServlet'
2025-06-11 10:43:22,838 INFO o.s.w.s.FrameworkServlet [http-nio-8095-exec-1] Completed initialization in 4 ms
2025-06-11 10:43:23,050 INFO c.m.s.s.c.ConfirmMoveInstructionController [http-nio-8095-exec-1] Received confirm movement instruction request: ConfirmMoveInstructionInput.Root(prefix=ConfirmMoveInstructionInput.Prefix(input=ConfirmMoveInstructionInput.Input(moveInstructionId=1812, userAlias=<EMAIL>)))
2025-06-11 10:43:23,545 INFO c.m.s.s.s.ConfirmMoveInstructionService [http-nio-8095-exec-1] Starting confirm move instruction for ID: 1812 by user: <EMAIL>
2025-06-11 10:43:24,253 INFO c.m.s.s.s.ConfirmMoveInstructionService [http-nio-8095-exec-1] Confirmed status ID: 42896
2025-06-11 10:43:26,043 INFO c.m.s.s.c.ConfirmMoveInstructionController [http-nio-8095-exec-1] Movement instruction confirmation completed with status: BUSINESS_ERROR
2025-06-11 10:53:11,083 INFO c.m.s.s.c.ConfirmMoveInstructionController [http-nio-8095-exec-3] Received confirm movement instruction request: ConfirmMoveInstructionInput.Root(prefix=ConfirmMoveInstructionInput.Prefix(input=ConfirmMoveInstructionInput.Input(moveInstructionId=1841, userAlias=<EMAIL>)))
2025-06-11 10:53:11,578 INFO c.m.s.s.s.ConfirmMoveInstructionService [http-nio-8095-exec-3] Starting confirm move instruction for ID: 1841 by user: <EMAIL>
2025-06-11 10:53:12,097 INFO c.m.s.s.s.ConfirmMoveInstructionService [http-nio-8095-exec-3] Confirmed status ID: 42896
2025-06-11 10:53:13,897 INFO c.m.s.s.c.ConfirmMoveInstructionController [http-nio-8095-exec-3] Movement instruction confirmation completed with status: BUSINESS_ERROR
2025-06-11 10:59:20,373 INFO o.s.o.j.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:59:20,391 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown initiated...
2025-06-11 10:59:20,409 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown completed.
2025-06-11 10:59:32,822 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 22868 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-11 10:59:32,830 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-11 10:59:40,587 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 10:59:42,624 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 2025 ms. Found 297 JPA repository interfaces.
2025-06-11 10:59:44,902 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8095 (http)
2025-06-11 10:59:44,922 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8095"]
2025-06-11 10:59:44,927 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-11 10:59:44,927 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-11 10:59:45,060 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-11 10:59:45,068 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 12089 ms
2025-06-11 10:59:47,096 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-11 10:59:50,330 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: 7fc75cc5-1ea2-48ce-be94-44e076141b8e
2025-06-11 10:59:50,330 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-11 10:59:50,428 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 10:59:50,581 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-11 10:59:50,639 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-11 10:59:51,185 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-11 10:59:54,178 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-11 10:59:54,178 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-11 10:59:58,510 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-11 10:59:58,544 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:59:59,428 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-11 11:00:03,306 WARN o.s.c.s.AbstractApplicationContext [main] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planificacionCambioZonaUseCaseImpl' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\business\core\planing\usecase\planificacion\PlanificacionCambioZonaUseCaseImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract com.maersk.sd1.sdy.dto.MovementInstructionLocationDto com.maersk.sd1.common.repository.MovementInstructionRepository.findLocationDtoByInstructionId(java.lang.Integer); Reason: Validation failed for query for method public abstract com.maersk.sd1.sdy.dto.MovementInstructionLocationDto com.maersk.sd1.common.repository.MovementInstructionRepository.findLocationDtoByInstructionId(java.lang.Integer)
2025-06-11 11:00:03,306 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 11:00:03,306 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Shutdown initiated...
2025-06-11 11:00:03,320 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Shutdown completed.
2025-06-11 11:00:04,326 WARN z.r.i.AsyncReporter$BoundedAsyncReporter [main] Timed out waiting for in-flight spans to send
2025-06-11 11:00:04,329 INFO o.a.j.l.DirectJDKLog [main] Stopping service [Tomcat]
2025-06-11 11:00:04,352 INFO o.s.b.a.l.ConditionEvaluationReportLogger [main] 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-11 11:00:04,388 ERROR o.s.b.SpringApplication [main] Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planificacionCambioZonaUseCaseImpl' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\business\core\planing\usecase\planificacion\PlanificacionCambioZonaUseCaseImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract com.maersk.sd1.sdy.dto.MovementInstructionLocationDto com.maersk.sd1.common.repository.MovementInstructionRepository.findLocationDtoByInstructionId(java.lang.Integer); Reason: Validation failed for query for method public abstract com.maersk.sd1.sdy.dto.MovementInstructionLocationDto com.maersk.sd1.common.repository.MovementInstructionRepository.findLocationDtoByInstructionId(java.lang.Integer)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.maersk.sd1.BusinessApplication.main(BusinessApplication.java:13)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract com.maersk.sd1.sdy.dto.MovementInstructionLocationDto com.maersk.sd1.common.repository.MovementInstructionRepository.findLocationDtoByInstructionId(java.lang.Integer); Reason: Validation failed for query for method public abstract com.maersk.sd1.sdy.dto.MovementInstructionLocationDto com.maersk.sd1.common.repository.MovementInstructionRepository.findLocationDtoByInstructionId(java.lang.Integer)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:409)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1185)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract com.maersk.sd1.sdy.dto.MovementInstructionLocationDto com.maersk.sd1.common.repository.MovementInstructionRepository.findLocationDtoByInstructionId(java.lang.Integer); Reason: Validation failed for query for method public abstract com.maersk.sd1.sdy.dto.MovementInstructionLocationDto com.maersk.sd1.common.repository.MovementInstructionRepository.findLocationDtoByInstructionId(java.lang.Integer)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 43 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract com.maersk.sd1.sdy.dto.MovementInstructionLocationDto com.maersk.sd1.common.repository.MovementInstructionRepository.findLocationDtoByInstructionId(java.lang.Integer); Reason: Validation failed for query for method public abstract com.maersk.sd1.sdy.dto.MovementInstructionLocationDto com.maersk.sd1.common.repository.MovementInstructionRepository.findLocationDtoByInstructionId(java.lang.Integer)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1806)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 56 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract com.maersk.sd1.sdy.dto.MovementInstructionLocationDto com.maersk.sd1.common.repository.MovementInstructionRepository.findLocationDtoByInstructionId(java.lang.Integer); Reason: Validation failed for query for method public abstract com.maersk.sd1.sdy.dto.MovementInstructionLocationDto com.maersk.sd1.common.repository.MovementInstructionRepository.findLocationDtoByInstructionId(java.lang.Integer)
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:119)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:103)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:357)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:290)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:296)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1802)
	... 67 common frames omitted
Caused by: java.lang.IllegalArgumentException: Validation failed for query for method public abstract com.maersk.sd1.sdy.dto.MovementInstructionLocationDto com.maersk.sd1.common.repository.MovementInstructionRepository.findLocationDtoByInstructionId(java.lang.Integer)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:100)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.<init>(SimpleJpaQuery.java:70)
	at org.springframework.data.jpa.repository.query.JpaQueryFactory.fromMethodWithQueryString(JpaQueryFactory.java:60)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$DeclaredQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:170)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:252)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:95)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:115)
	... 79 common frames omitted
Caused by: java.lang.IllegalArgumentException: org.hibernate.query.sqm.UnknownPathException: Could not resolve attribute 'cat' of 'com.maersk.sd1.common.model.Block' [SELECT new com.maersk.sd1.sdy.dto.MovementInstructionLocationDto(i.originBlock.id, i.originCell.id, i.originLevel.id, i.origin40Block.id, i.origin40Cell.id, i.origin40Level.id, i.destinationBlock.id, i.destinationCell.id, i.destinationLevel.id, i.destination40Block.id, i.destination40Cell.id, i.destination40Level.id, i.container.id, statusCat.code, statusCat.description, destBlockCat.code, originBlockCat.code, destBlock.code) FROM MovementInstruction i JOIN i.catStatus statusCat JOIN i.originBlock originBlock JOIN i.destinationBlock destBlock LEFT JOIN originBlock.cat originBlockCat LEFT JOIN destBlock.cat destBlockCat WHERE i.id = :moveInstructionId]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:143)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:167)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:173)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:802)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:707)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:132)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.ExtendedEntityManagerCreator$ExtendedEntityManagerInvocationHandler.invoke(ExtendedEntityManagerCreator.java:364)
	at jdk.proxy2/jdk.proxy2.$Proxy173.createQuery(Unknown Source)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:94)
	... 85 common frames omitted
Caused by: org.hibernate.query.sqm.UnknownPathException: Could not resolve attribute 'cat' of 'com.maersk.sd1.common.model.Block' [SELECT new com.maersk.sd1.sdy.dto.MovementInstructionLocationDto(i.originBlock.id, i.originCell.id, i.originLevel.id, i.origin40Block.id, i.origin40Cell.id, i.origin40Level.id, i.destinationBlock.id, i.destinationCell.id, i.destinationLevel.id, i.destination40Block.id, i.destination40Cell.id, i.destination40Level.id, i.container.id, statusCat.code, statusCat.description, destBlockCat.code, originBlockCat.code, destBlock.code) FROM MovementInstruction i JOIN i.catStatus statusCat JOIN i.originBlock originBlock JOIN i.destinationBlock destBlock LEFT JOIN originBlock.cat originBlockCat LEFT JOIN destBlock.cat destBlockCat WHERE i.id = :moveInstructionId]
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:87)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.createHqlInterpretation(QueryInterpretationCacheStandardImpl.java:165)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.resolveHqlInterpretation(QueryInterpretationCacheStandardImpl.java:147)
	at org.hibernate.internal.AbstractSharedSessionContract.interpretHql(AbstractSharedSessionContract.java:744)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:794)
	... 92 common frames omitted
Caused by: org.hibernate.query.sqm.PathElementException: Could not resolve attribute 'cat' of 'com.maersk.sd1.common.model.Block'
	at org.hibernate.query.sqm.SqmPathSource.getSubPathSource(SqmPathSource.java:95)
	at org.hibernate.query.hql.internal.QualifiedJoinPathConsumer.subPathSource(QualifiedJoinPathConsumer.java:211)
	at org.hibernate.query.hql.internal.QualifiedJoinPathConsumer.createJoin(QualifiedJoinPathConsumer.java:189)
	at org.hibernate.query.hql.internal.QualifiedJoinPathConsumer$AttributeJoinDelegate.consumeIdentifier(QualifiedJoinPathConsumer.java:265)
	at org.hibernate.query.hql.internal.QualifiedJoinPathConsumer.consumeIdentifier(QualifiedJoinPathConsumer.java:103)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimplePath(SemanticQueryBuilder.java:5010)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitIndexedPathAccessFragment(SemanticQueryBuilder.java:4959)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitGeneralPathFragment(SemanticQueryBuilder.java:4934)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitGeneralPathFragment(SemanticQueryBuilder.java:268)
	at org.hibernate.grammars.hql.HqlParser$GeneralPathFragmentContext.accept(HqlParser.java:4390)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitPath(SemanticQueryBuilder.java:4925)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitPath(SemanticQueryBuilder.java:268)
	at org.hibernate.grammars.hql.HqlParser$PathContext.accept(HqlParser.java:4188)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.getJoin(SemanticQueryBuilder.java:2162)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.consumeJoin(SemanticQueryBuilder.java:2106)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitEntityWithJoins(SemanticQueryBuilder.java:1921)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitFromClause(SemanticQueryBuilder.java:1900)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuery(SemanticQueryBuilder.java:1147)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuerySpecExpression(SemanticQueryBuilder.java:940)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuerySpecExpression(SemanticQueryBuilder.java:268)
	at org.hibernate.grammars.hql.HqlParser$QuerySpecExpressionContext.accept(HqlParser.java:1844)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimpleQueryGroup(SemanticQueryBuilder.java:925)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimpleQueryGroup(SemanticQueryBuilder.java:268)
	at org.hibernate.grammars.hql.HqlParser$SimpleQueryGroupContext.accept(HqlParser.java:1718)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSelectStatement(SemanticQueryBuilder.java:442)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitStatement(SemanticQueryBuilder.java:401)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.buildSemanticModel(SemanticQueryBuilder.java:310)
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:71)
	... 96 common frames omitted
2025-06-11 11:06:35,833 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 7732 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-11 11:06:35,837 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-11 11:06:41,758 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 11:06:43,547 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 1776 ms. Found 297 JPA repository interfaces.
2025-06-11 11:06:45,201 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8095 (http)
2025-06-11 11:06:45,219 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8095"]
2025-06-11 11:06:45,226 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-11 11:06:45,229 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-11 11:06:45,320 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-11 11:06:45,320 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 9308 ms
2025-06-11 11:06:47,064 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-11 11:06:49,849 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: 5b08d851-f53e-4a21-a398-f51d120768e7
2025-06-11 11:06:49,850 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-11 11:06:49,925 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 11:06:49,990 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-11 11:06:50,047 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-11 11:06:50,446 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-11 11:06:53,041 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-11 11:06:53,041 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-11 11:06:56,737 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-11 11:06:56,771 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 11:06:57,685 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-11 11:07:18,318 WARN o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration [main] spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 11:07:21,722 INFO o.s.b.a.e.w.EndpointLinksResolver [main] Exposing 1 endpoint beneath base path '/actuator'
2025-06-11 11:07:22,011 INFO o.a.j.l.DirectJDKLog [main] Starting ProtocolHandler ["http-nio-8095"]
2025-06-11 11:07:22,087 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat started on port 8095 (http) with context path '/'
2025-06-11 11:07:22,124 INFO o.s.b.StartupInfoLogger [main] Started BusinessApplication in 47.524 seconds (process running for 48.853)
2025-06-11 11:07:25,397 INFO o.a.j.l.DirectJDKLog [http-nio-8095-exec-1] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 11:07:25,399 INFO o.s.w.s.FrameworkServlet [http-nio-8095-exec-1] Initializing Servlet 'dispatcherServlet'
2025-06-11 11:07:25,405 INFO o.s.w.s.FrameworkServlet [http-nio-8095-exec-1] Completed initialization in 6 ms
2025-06-11 11:07:25,628 INFO c.m.s.s.c.ConfirmMoveInstructionController [http-nio-8095-exec-1] Received confirm movement instruction request: ConfirmMoveInstructionInput.Root(prefix=ConfirmMoveInstructionInput.Prefix(input=ConfirmMoveInstructionInput.Input(moveInstructionId=1841, userAlias=<EMAIL>)))
2025-06-11 11:07:26,094 INFO c.m.s.s.s.ConfirmMoveInstructionService [http-nio-8095-exec-1] Starting confirm move instruction for ID: 1841 by user: <EMAIL>
2025-06-11 11:07:26,826 INFO c.m.s.s.s.ConfirmMoveInstructionService [http-nio-8095-exec-1] Confirmed status ID: 42896
2025-06-11 11:07:27,840 INFO c.m.s.s.s.ConfirmMoveInstructionService [http-nio-8095-exec-1] Details: MovementInstructionLocationDto(blockIdOrigin=93, cellIdOrigin=911, levelIdOrigin=4553, block40IdOrigin=93, cell40IdOrigin=921, level40IdOrigin=4653, blockIdDestination=93, cellIdDestination=910, levelIdDestination=4540, block40IdDestination=93, celda40IdDestino=920, nivel40IdDestino=4640, containerId=2531, statusMovementInstructionCode=EXE, statusMovementInstructionDescription=EJECUTADO, blockDestinationType=STACK, blockOriginType=STACK, blockDestinationCode=SCTG1)
2025-06-11 11:07:28,086 INFO c.m.s.s.s.ConfirmMoveInstructionService [http-nio-8095-exec-1] Removing container from origin location for instruction: 1841
2025-06-11 11:07:28,881 INFO c.m.s.s.s.ConfirmMoveInstructionService [http-nio-8095-exec-1] Movement instruction 1841 executed successfully
2025-06-11 11:07:29,857 ERROR c.m.s.s.s.ConfirmMoveInstructionService [http-nio-8095-exec-1] Error confirming movement instruction
org.springframework.dao.InvalidDataAccessApiUsageException: Cannot instantiate class 'com.maersk.sd1.sdy.dto.MovementInstructionConfirmOutput' (it has no constructor with signature [java.lang.Integer, java.lang.Integer, java.lang.Integer, java.lang.String, java.lang.String, java.lang.String, java.lang.Integer, java.lang.String, java.lang.String, java.lang.String, java.lang.Integer, java.lang.Integer, java.lang.String, java.lang.String, java.lang.String, java.lang.Integer, java.lang.String, java.lang.String, java.lang.String, java.lang.Integer, java.lang.String, java.lang.Integer, java.lang.String, java.lang.String, java.lang.String, java.lang.String], and not every argument has an alias)
	at org.springframework.orm.jpa.EntityManagerFactoryUtils.convertJpaAccessExceptionIfPossible(EntityManagerFactoryUtils.java:368)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:246)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:136)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy185.getFinalResult(Unknown Source)
	at com.maersk.sd1.sdy.service.ConfirmMoveInstructionService.buildSuccessOutput(ConfirmMoveInstructionService.java:372)
	at com.maersk.sd1.sdy.service.ConfirmMoveInstructionService.confirmMoveInstruction(ConfirmMoveInstructionService.java:105)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:379)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.maersk.sd1.sdy.service.ConfirmMoveInstructionService$$SpringCGLIB$$0.confirmMoveInstruction(<generated>)
	at com.maersk.sd1.sdy.controller.ConfirmMoveInstructionController.confirmMoveInstruction(ConfirmMoveInstructionController.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:384)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1575)
Caused by: java.lang.IllegalStateException: Cannot instantiate class 'com.maersk.sd1.sdy.dto.MovementInstructionConfirmOutput' (it has no constructor with signature [java.lang.Integer, java.lang.Integer, java.lang.Integer, java.lang.String, java.lang.String, java.lang.String, java.lang.Integer, java.lang.String, java.lang.String, java.lang.String, java.lang.Integer, java.lang.Integer, java.lang.String, java.lang.String, java.lang.String, java.lang.Integer, java.lang.String, java.lang.String, java.lang.String, java.lang.Integer, java.lang.String, java.lang.Integer, java.lang.String, java.lang.String, java.lang.String, java.lang.String], and not every argument has an alias)
	at org.hibernate.sql.results.graph.instantiation.internal.DynamicInstantiationResultImpl.resolveAssembler(DynamicInstantiationResultImpl.java:187)
	at org.hibernate.sql.results.graph.instantiation.internal.DynamicInstantiationResultImpl.createResultAssembler(DynamicInstantiationResultImpl.java:107)
	at org.hibernate.sql.results.jdbc.internal.StandardJdbcValuesMapping.resolveAssemblers(StandardJdbcValuesMapping.java:53)
	at org.hibernate.sql.results.internal.ResultsHelper.createRowReader(ResultsHelper.java:76)
	at org.hibernate.sql.results.internal.ResultsHelper.createRowReader(ResultsHelper.java:62)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:340)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:168)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.list(JdbcSelectExecutorStandardImpl.java:93)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:31)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$0(ConcreteSqmSelectQueryPlan.java:109)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:305)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:246)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:509)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:427)
	at org.hibernate.query.spi.AbstractSelectionQuery.getSingleResult(AbstractSelectionQuery.java:559)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution$SingleEntityExecution.doExecute(JpaQueryExecution.java:223)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution.execute(JpaQueryExecution.java:92)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.doExecute(AbstractJpaQuery.java:152)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.execute(AbstractJpaQuery.java:140)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:169)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:148)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:379)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 74 common frames omitted
2025-06-11 11:07:30,439 INFO c.m.s.s.c.ConfirmMoveInstructionController [http-nio-8095-exec-1] Movement instruction confirmation completed with status: BUSINESS_ERROR
2025-06-11 14:31:12,395 INFO o.s.o.j.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 14:31:12,602 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown initiated...
2025-06-11 14:31:12,677 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown completed.
2025-06-11 14:31:27,415 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 30864 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-11 14:31:27,420 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-11 14:31:37,400 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 14:31:39,160 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 1749 ms. Found 297 JPA repository interfaces.
2025-06-11 14:31:40,965 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8095 (http)
2025-06-11 14:31:40,982 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8095"]
2025-06-11 14:31:40,988 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-11 14:31:40,989 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-11 14:31:41,125 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-11 14:31:41,128 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 13553 ms
2025-06-11 14:31:43,037 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-11 14:31:45,857 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: 1b7a53ce-b628-4db4-adcb-ff951e3b1556
2025-06-11 14:31:45,859 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-11 14:31:45,947 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 14:31:46,089 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-11 14:31:46,166 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-11 14:31:46,672 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-11 14:31:49,512 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-11 14:31:49,513 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-11 14:31:53,254 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-11 14:31:53,287 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 14:31:54,161 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-11 14:31:57,062 WARN o.s.c.s.AbstractApplicationContext [main] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planificacionCambioZonaUseCaseImpl' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\business\core\planing\usecase\planificacion\PlanificacionCambioZonaUseCaseImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 4: Error creating bean with name 'containerLocationRepository' defined in com.maersk.sd1.common.repository.ContainerLocationRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.clearContainerFromLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer); Reason: Validation failed for query for method public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.clearContainerFromLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-06-11 14:31:57,063 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 14:31:57,068 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Shutdown initiated...
2025-06-11 14:31:57,614 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Shutdown completed.
2025-06-11 14:31:58,626 WARN z.r.i.AsyncReporter$BoundedAsyncReporter [main] Timed out waiting for in-flight spans to send
2025-06-11 14:31:58,630 INFO o.a.j.l.DirectJDKLog [main] Stopping service [Tomcat]
2025-06-11 14:31:58,649 INFO o.s.b.a.l.ConditionEvaluationReportLogger [main] 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-11 14:31:58,675 ERROR o.s.b.SpringApplication [main] Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planificacionCambioZonaUseCaseImpl' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\business\core\planing\usecase\planificacion\PlanificacionCambioZonaUseCaseImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 4: Error creating bean with name 'containerLocationRepository' defined in com.maersk.sd1.common.repository.ContainerLocationRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.clearContainerFromLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer); Reason: Validation failed for query for method public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.clearContainerFromLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.maersk.sd1.BusinessApplication.main(BusinessApplication.java:13)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 4: Error creating bean with name 'containerLocationRepository' defined in com.maersk.sd1.common.repository.ContainerLocationRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.clearContainerFromLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer); Reason: Validation failed for query for method public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.clearContainerFromLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:409)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1185)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 4: Error creating bean with name 'containerLocationRepository' defined in com.maersk.sd1.common.repository.ContainerLocationRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.clearContainerFromLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer); Reason: Validation failed for query for method public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.clearContainerFromLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 43 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'containerLocationRepository' defined in com.maersk.sd1.common.repository.ContainerLocationRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.clearContainerFromLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer); Reason: Validation failed for query for method public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.clearContainerFromLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1806)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 56 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.clearContainerFromLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer); Reason: Validation failed for query for method public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.clearContainerFromLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer)
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:119)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:103)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:357)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:290)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:296)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1802)
	... 67 common frames omitted
Caused by: java.lang.IllegalArgumentException: Validation failed for query for method public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.clearContainerFromLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:100)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.<init>(SimpleJpaQuery.java:70)
	at org.springframework.data.jpa.repository.query.JpaQueryFactory.fromMethodWithQueryString(JpaQueryFactory.java:60)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$DeclaredQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:170)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:252)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:95)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:115)
	... 79 common frames omitted
Caused by: java.lang.IllegalArgumentException: org.hibernate.query.SyntaxException: At 1:59 and token 'cl', missing '=' at 'cl' [UPDATE ContainerLocation cl SET cl.container = null, WHERE cl.block.id = :blockId AND cl.cell.id = :cellId AND cl.level.id = :levelId ]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:143)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:167)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:173)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:802)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:707)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:132)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.ExtendedEntityManagerCreator$ExtendedEntityManagerInvocationHandler.invoke(ExtendedEntityManagerCreator.java:364)
	at jdk.proxy2/jdk.proxy2.$Proxy173.createQuery(Unknown Source)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:94)
	... 85 common frames omitted
Caused by: org.hibernate.query.SyntaxException: At 1:59 and token 'cl', missing '=' at 'cl' [UPDATE ContainerLocation cl SET cl.container = null, WHERE cl.block.id = :blockId AND cl.cell.id = :cellId AND cl.level.id = :levelId ]
	at org.hibernate.query.hql.internal.StandardHqlTranslator$1.syntaxError(StandardHqlTranslator.java:108)
	at org.antlr.v4.runtime.ProxyErrorListener.syntaxError(ProxyErrorListener.java:41)
	at org.antlr.v4.runtime.Parser.notifyErrorListeners(Parser.java:543)
	at org.antlr.v4.runtime.DefaultErrorStrategy.reportMissingToken(DefaultErrorStrategy.java:409)
	at org.antlr.v4.runtime.DefaultErrorStrategy.singleTokenInsertion(DefaultErrorStrategy.java:519)
	at org.antlr.v4.runtime.DefaultErrorStrategy.recoverInline(DefaultErrorStrategy.java:476)
	at org.antlr.v4.runtime.Parser.match(Parser.java:207)
	at org.hibernate.grammars.hql.HqlParser.assignment(HqlParser.java:776)
	at org.hibernate.grammars.hql.HqlParser.setClause(HqlParser.java:720)
	at org.hibernate.grammars.hql.HqlParser.updateStatement(HqlParser.java:645)
	at org.hibernate.grammars.hql.HqlParser.statement(HqlParser.java:342)
	at org.hibernate.query.hql.internal.StandardHqlTranslator.parseHql(StandardHqlTranslator.java:143)
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:67)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.createHqlInterpretation(QueryInterpretationCacheStandardImpl.java:165)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.resolveHqlInterpretation(QueryInterpretationCacheStandardImpl.java:147)
	at org.hibernate.internal.AbstractSharedSessionContract.interpretHql(AbstractSharedSessionContract.java:744)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:794)
	... 92 common frames omitted
2025-06-11 14:33:30,988 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 10928 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-11 14:33:30,991 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-11 14:33:38,281 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 14:33:40,334 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 2040 ms. Found 297 JPA repository interfaces.
2025-06-11 14:33:42,724 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8095 (http)
2025-06-11 14:33:42,751 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8095"]
2025-06-11 14:33:42,761 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-11 14:33:42,762 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-11 14:33:42,910 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-11 14:33:42,913 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 11839 ms
2025-06-11 14:33:44,656 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-11 14:33:46,984 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: b8dc22a3-3ef9-4efe-a9c1-90cf65685011
2025-06-11 14:33:46,986 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-11 14:33:47,051 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 14:33:47,159 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-11 14:33:47,233 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-11 14:33:47,678 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-11 14:33:50,364 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-11 14:33:50,364 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-11 14:33:54,452 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-11 14:33:54,490 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 14:33:55,303 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-11 14:33:58,038 WARN o.s.c.s.AbstractApplicationContext [main] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planificacionCambioZonaUseCaseImpl' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\business\core\planing\usecase\planificacion\PlanificacionCambioZonaUseCaseImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 4: Error creating bean with name 'containerLocationRepository' defined in com.maersk.sd1.common.repository.ContainerLocationRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract com.maersk.sd1.sdy.dto.ConfirmMoveInstructionOutput com.maersk.sd1.common.repository.ContainerLocationRepository.findConfirmMoveInstructionOutput(java.lang.Integer,java.lang.String,java.lang.String); Reason: Validation failed for query for method public abstract com.maersk.sd1.sdy.dto.ConfirmMoveInstructionOutput com.maersk.sd1.common.repository.ContainerLocationRepository.findConfirmMoveInstructionOutput(java.lang.Integer,java.lang.String,java.lang.String)
2025-06-11 14:33:58,039 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 14:33:58,043 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Shutdown initiated...
2025-06-11 14:33:58,947 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Shutdown completed.
2025-06-11 14:33:59,957 WARN z.r.i.AsyncReporter$BoundedAsyncReporter [main] Timed out waiting for in-flight spans to send
2025-06-11 14:33:59,961 INFO o.a.j.l.DirectJDKLog [main] Stopping service [Tomcat]
2025-06-11 14:33:59,977 INFO o.s.b.a.l.ConditionEvaluationReportLogger [main] 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-11 14:33:59,998 ERROR o.s.b.SpringApplication [main] Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planificacionCambioZonaUseCaseImpl' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\business\core\planing\usecase\planificacion\PlanificacionCambioZonaUseCaseImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 4: Error creating bean with name 'containerLocationRepository' defined in com.maersk.sd1.common.repository.ContainerLocationRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract com.maersk.sd1.sdy.dto.ConfirmMoveInstructionOutput com.maersk.sd1.common.repository.ContainerLocationRepository.findConfirmMoveInstructionOutput(java.lang.Integer,java.lang.String,java.lang.String); Reason: Validation failed for query for method public abstract com.maersk.sd1.sdy.dto.ConfirmMoveInstructionOutput com.maersk.sd1.common.repository.ContainerLocationRepository.findConfirmMoveInstructionOutput(java.lang.Integer,java.lang.String,java.lang.String)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.maersk.sd1.BusinessApplication.main(BusinessApplication.java:13)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 4: Error creating bean with name 'containerLocationRepository' defined in com.maersk.sd1.common.repository.ContainerLocationRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract com.maersk.sd1.sdy.dto.ConfirmMoveInstructionOutput com.maersk.sd1.common.repository.ContainerLocationRepository.findConfirmMoveInstructionOutput(java.lang.Integer,java.lang.String,java.lang.String); Reason: Validation failed for query for method public abstract com.maersk.sd1.sdy.dto.ConfirmMoveInstructionOutput com.maersk.sd1.common.repository.ContainerLocationRepository.findConfirmMoveInstructionOutput(java.lang.Integer,java.lang.String,java.lang.String)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:409)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1185)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 4: Error creating bean with name 'containerLocationRepository' defined in com.maersk.sd1.common.repository.ContainerLocationRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract com.maersk.sd1.sdy.dto.ConfirmMoveInstructionOutput com.maersk.sd1.common.repository.ContainerLocationRepository.findConfirmMoveInstructionOutput(java.lang.Integer,java.lang.String,java.lang.String); Reason: Validation failed for query for method public abstract com.maersk.sd1.sdy.dto.ConfirmMoveInstructionOutput com.maersk.sd1.common.repository.ContainerLocationRepository.findConfirmMoveInstructionOutput(java.lang.Integer,java.lang.String,java.lang.String)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 43 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'containerLocationRepository' defined in com.maersk.sd1.common.repository.ContainerLocationRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract com.maersk.sd1.sdy.dto.ConfirmMoveInstructionOutput com.maersk.sd1.common.repository.ContainerLocationRepository.findConfirmMoveInstructionOutput(java.lang.Integer,java.lang.String,java.lang.String); Reason: Validation failed for query for method public abstract com.maersk.sd1.sdy.dto.ConfirmMoveInstructionOutput com.maersk.sd1.common.repository.ContainerLocationRepository.findConfirmMoveInstructionOutput(java.lang.Integer,java.lang.String,java.lang.String)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1806)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 56 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract com.maersk.sd1.sdy.dto.ConfirmMoveInstructionOutput com.maersk.sd1.common.repository.ContainerLocationRepository.findConfirmMoveInstructionOutput(java.lang.Integer,java.lang.String,java.lang.String); Reason: Validation failed for query for method public abstract com.maersk.sd1.sdy.dto.ConfirmMoveInstructionOutput com.maersk.sd1.common.repository.ContainerLocationRepository.findConfirmMoveInstructionOutput(java.lang.Integer,java.lang.String,java.lang.String)
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:119)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:103)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:357)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:290)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:296)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1802)
	... 67 common frames omitted
Caused by: java.lang.IllegalArgumentException: Validation failed for query for method public abstract com.maersk.sd1.sdy.dto.ConfirmMoveInstructionOutput com.maersk.sd1.common.repository.ContainerLocationRepository.findConfirmMoveInstructionOutput(java.lang.Integer,java.lang.String,java.lang.String)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:100)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.<init>(SimpleJpaQuery.java:70)
	at org.springframework.data.jpa.repository.query.JpaQueryFactory.fromMethodWithQueryString(JpaQueryFactory.java:60)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$DeclaredQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:170)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:252)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:95)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:115)
	... 79 common frames omitted
Caused by: java.lang.IllegalArgumentException: org.hibernate.query.sqm.UnknownPathException: Could not resolve attribute 'cat' of 'com.maersk.sd1.common.model.Block' [SELECT new com.maersk.sd1.sdy.dto.ConfirmMoveInstructionOutput(
    i.id,
    uDest.id,
    u40Dest.id,
    bOrigin.id,
    bOrigin.code,
    cOrigin.column,
    cOrigin.row,
    nOrigin.index,
    b40Origin.code,
    c40Origin.column,
    c40Origin.row,
    n40Origin.index,
    bDest.id,
    bDest.code,
    cDest.column,
    cDest.row,
    nDest.index,
    b40Dest.code,
    c40Dest.column,
    c40Dest.row,
    n40Dest.index,
    cat.description,
    i.catStatus.id,
    g.code,
    g.description,
    catlg.code,
    :respEstado,
    :respMensaje
)
FROM MovementInstruction i
JOIN i.container con
JOIN con.catSize cat
LEFT JOIN i.originBlock bOrigin
LEFT JOIN i.originCell cOrigin
LEFT JOIN i.originLevel nOrigin
LEFT JOIN i.origin40Block b40Origin
LEFT JOIN i.origin40Cell c40Origin
LEFT JOIN i.origin40Level n40Origin
LEFT JOIN i.destinationBlock bDest
LEFT JOIN i.destinationCell cDest
LEFT JOIN i.destinationLevel nDest
LEFT JOIN i.destination40Block b40Dest
LEFT JOIN i.destination40Cell c40Dest
LEFT JOIN i.destination40Level n40Dest
LEFT JOIN ContainerLocation uDest ON uDest.block.id = bDest.id AND uDest.cell.id = cDest.id AND uDest.level.id = nDest.id
LEFT JOIN ContainerLocation u40Dest ON u40Dest.block.id = b40Dest.id AND u40Dest.cell.id = c40Dest.id AND u40Dest.level.id = n40Dest.id
LEFT JOIN i.catStatus g
LEFT JOIN bDest.cat catlg
WHERE i.id = :moveInstructionId
]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:143)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:167)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:173)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:802)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:707)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:132)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.ExtendedEntityManagerCreator$ExtendedEntityManagerInvocationHandler.invoke(ExtendedEntityManagerCreator.java:364)
	at jdk.proxy2/jdk.proxy2.$Proxy173.createQuery(Unknown Source)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:94)
	... 85 common frames omitted
Caused by: org.hibernate.query.sqm.UnknownPathException: Could not resolve attribute 'cat' of 'com.maersk.sd1.common.model.Block' [SELECT new com.maersk.sd1.sdy.dto.ConfirmMoveInstructionOutput(
    i.id,
    uDest.id,
    u40Dest.id,
    bOrigin.id,
    bOrigin.code,
    cOrigin.column,
    cOrigin.row,
    nOrigin.index,
    b40Origin.code,
    c40Origin.column,
    c40Origin.row,
    n40Origin.index,
    bDest.id,
    bDest.code,
    cDest.column,
    cDest.row,
    nDest.index,
    b40Dest.code,
    c40Dest.column,
    c40Dest.row,
    n40Dest.index,
    cat.description,
    i.catStatus.id,
    g.code,
    g.description,
    catlg.code,
    :respEstado,
    :respMensaje
)
FROM MovementInstruction i
JOIN i.container con
JOIN con.catSize cat
LEFT JOIN i.originBlock bOrigin
LEFT JOIN i.originCell cOrigin
LEFT JOIN i.originLevel nOrigin
LEFT JOIN i.origin40Block b40Origin
LEFT JOIN i.origin40Cell c40Origin
LEFT JOIN i.origin40Level n40Origin
LEFT JOIN i.destinationBlock bDest
LEFT JOIN i.destinationCell cDest
LEFT JOIN i.destinationLevel nDest
LEFT JOIN i.destination40Block b40Dest
LEFT JOIN i.destination40Cell c40Dest
LEFT JOIN i.destination40Level n40Dest
LEFT JOIN ContainerLocation uDest ON uDest.block.id = bDest.id AND uDest.cell.id = cDest.id AND uDest.level.id = nDest.id
LEFT JOIN ContainerLocation u40Dest ON u40Dest.block.id = b40Dest.id AND u40Dest.cell.id = c40Dest.id AND u40Dest.level.id = n40Dest.id
LEFT JOIN i.catStatus g
LEFT JOIN bDest.cat catlg
WHERE i.id = :moveInstructionId
]
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:87)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.createHqlInterpretation(QueryInterpretationCacheStandardImpl.java:165)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.resolveHqlInterpretation(QueryInterpretationCacheStandardImpl.java:147)
	at org.hibernate.internal.AbstractSharedSessionContract.interpretHql(AbstractSharedSessionContract.java:744)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:794)
	... 92 common frames omitted
Caused by: org.hibernate.query.sqm.PathElementException: Could not resolve attribute 'cat' of 'com.maersk.sd1.common.model.Block'
	at org.hibernate.query.sqm.SqmPathSource.getSubPathSource(SqmPathSource.java:95)
	at org.hibernate.query.hql.internal.QualifiedJoinPathConsumer.subPathSource(QualifiedJoinPathConsumer.java:211)
	at org.hibernate.query.hql.internal.QualifiedJoinPathConsumer.createJoin(QualifiedJoinPathConsumer.java:189)
	at org.hibernate.query.hql.internal.QualifiedJoinPathConsumer$AttributeJoinDelegate.consumeIdentifier(QualifiedJoinPathConsumer.java:265)
	at org.hibernate.query.hql.internal.QualifiedJoinPathConsumer.consumeIdentifier(QualifiedJoinPathConsumer.java:103)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimplePath(SemanticQueryBuilder.java:5010)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitIndexedPathAccessFragment(SemanticQueryBuilder.java:4959)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitGeneralPathFragment(SemanticQueryBuilder.java:4934)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitGeneralPathFragment(SemanticQueryBuilder.java:268)
	at org.hibernate.grammars.hql.HqlParser$GeneralPathFragmentContext.accept(HqlParser.java:4390)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitPath(SemanticQueryBuilder.java:4925)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitPath(SemanticQueryBuilder.java:268)
	at org.hibernate.grammars.hql.HqlParser$PathContext.accept(HqlParser.java:4188)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.getJoin(SemanticQueryBuilder.java:2162)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.consumeJoin(SemanticQueryBuilder.java:2106)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitEntityWithJoins(SemanticQueryBuilder.java:1921)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitFromClause(SemanticQueryBuilder.java:1900)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuery(SemanticQueryBuilder.java:1147)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuerySpecExpression(SemanticQueryBuilder.java:940)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuerySpecExpression(SemanticQueryBuilder.java:268)
	at org.hibernate.grammars.hql.HqlParser$QuerySpecExpressionContext.accept(HqlParser.java:1844)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimpleQueryGroup(SemanticQueryBuilder.java:925)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimpleQueryGroup(SemanticQueryBuilder.java:268)
	at org.hibernate.grammars.hql.HqlParser$SimpleQueryGroupContext.accept(HqlParser.java:1718)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSelectStatement(SemanticQueryBuilder.java:442)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitStatement(SemanticQueryBuilder.java:401)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.buildSemanticModel(SemanticQueryBuilder.java:310)
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:71)
	... 96 common frames omitted
2025-06-11 14:38:22,873 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 26932 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-11 14:38:22,878 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-11 14:38:27,410 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 14:38:29,392 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 1970 ms. Found 297 JPA repository interfaces.
2025-06-11 14:38:31,090 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8095 (http)
2025-06-11 14:38:31,115 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8095"]
2025-06-11 14:38:31,120 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-11 14:38:31,120 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-11 14:38:31,228 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-11 14:38:31,229 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 8233 ms
2025-06-11 14:38:32,796 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-11 14:38:35,551 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: 25f0a712-3ada-48e4-acf0-a81f1cdf9499
2025-06-11 14:38:35,554 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-11 14:38:35,652 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 14:38:35,762 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-11 14:38:35,825 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-11 14:38:36,168 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-11 14:38:39,325 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-11 14:38:39,326 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-11 14:38:43,058 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-11 14:38:43,095 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 14:38:43,820 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-11 14:38:46,652 WARN o.s.c.s.AbstractApplicationContext [main] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planificacionCambioZonaUseCaseImpl' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\business\core\planing\usecase\planificacion\PlanificacionCambioZonaUseCaseImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 4: Error creating bean with name 'containerLocationRepository' defined in com.maersk.sd1.common.repository.ContainerLocationRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.updateContainerAtLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer); Reason: Validation failed for query for method public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.updateContainerAtLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-06-11 14:38:46,653 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 14:38:46,657 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Shutdown initiated...
2025-06-11 14:38:46,704 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Shutdown completed.
2025-06-11 14:38:47,709 WARN z.r.i.AsyncReporter$BoundedAsyncReporter [main] Timed out waiting for in-flight spans to send
2025-06-11 14:38:47,712 INFO o.a.j.l.DirectJDKLog [main] Stopping service [Tomcat]
2025-06-11 14:38:47,736 INFO o.s.b.a.l.ConditionEvaluationReportLogger [main] 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-11 14:38:47,773 ERROR o.s.b.SpringApplication [main] Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planificacionCambioZonaUseCaseImpl' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\business\core\planing\usecase\planificacion\PlanificacionCambioZonaUseCaseImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 4: Error creating bean with name 'containerLocationRepository' defined in com.maersk.sd1.common.repository.ContainerLocationRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.updateContainerAtLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer); Reason: Validation failed for query for method public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.updateContainerAtLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.maersk.sd1.BusinessApplication.main(BusinessApplication.java:13)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 4: Error creating bean with name 'containerLocationRepository' defined in com.maersk.sd1.common.repository.ContainerLocationRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.updateContainerAtLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer); Reason: Validation failed for query for method public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.updateContainerAtLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:409)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1185)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 4: Error creating bean with name 'containerLocationRepository' defined in com.maersk.sd1.common.repository.ContainerLocationRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.updateContainerAtLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer); Reason: Validation failed for query for method public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.updateContainerAtLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 43 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'containerLocationRepository' defined in com.maersk.sd1.common.repository.ContainerLocationRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.updateContainerAtLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer); Reason: Validation failed for query for method public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.updateContainerAtLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1806)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 56 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.updateContainerAtLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer); Reason: Validation failed for query for method public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.updateContainerAtLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer)
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:119)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:103)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:357)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:290)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:296)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1802)
	... 67 common frames omitted
Caused by: java.lang.IllegalArgumentException: Validation failed for query for method public abstract void com.maersk.sd1.common.repository.ContainerLocationRepository.updateContainerAtLocation(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:100)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.<init>(SimpleJpaQuery.java:70)
	at org.springframework.data.jpa.repository.query.JpaQueryFactory.fromMethodWithQueryString(JpaQueryFactory.java:60)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$DeclaredQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:170)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:252)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:95)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:115)
	... 79 common frames omitted
Caused by: java.lang.IllegalArgumentException: org.hibernate.query.SyntaxException: At 1:70 and token 'cl', missing '=' at 'cl' [UPDATE ContainerLocation cl SET cl.container.id = :containerId, WHERE cl.block.id = :blockId AND cl.cell.id = :cellId AND cl.level.id = :levelId ]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:143)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:167)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:173)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:802)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:707)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:132)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.ExtendedEntityManagerCreator$ExtendedEntityManagerInvocationHandler.invoke(ExtendedEntityManagerCreator.java:364)
	at jdk.proxy2/jdk.proxy2.$Proxy173.createQuery(Unknown Source)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:94)
	... 85 common frames omitted
Caused by: org.hibernate.query.SyntaxException: At 1:70 and token 'cl', missing '=' at 'cl' [UPDATE ContainerLocation cl SET cl.container.id = :containerId, WHERE cl.block.id = :blockId AND cl.cell.id = :cellId AND cl.level.id = :levelId ]
	at org.hibernate.query.hql.internal.StandardHqlTranslator$1.syntaxError(StandardHqlTranslator.java:108)
	at org.antlr.v4.runtime.ProxyErrorListener.syntaxError(ProxyErrorListener.java:41)
	at org.antlr.v4.runtime.Parser.notifyErrorListeners(Parser.java:543)
	at org.antlr.v4.runtime.DefaultErrorStrategy.reportMissingToken(DefaultErrorStrategy.java:409)
	at org.antlr.v4.runtime.DefaultErrorStrategy.singleTokenInsertion(DefaultErrorStrategy.java:519)
	at org.antlr.v4.runtime.DefaultErrorStrategy.recoverInline(DefaultErrorStrategy.java:476)
	at org.antlr.v4.runtime.Parser.match(Parser.java:207)
	at org.hibernate.grammars.hql.HqlParser.assignment(HqlParser.java:776)
	at org.hibernate.grammars.hql.HqlParser.setClause(HqlParser.java:720)
	at org.hibernate.grammars.hql.HqlParser.updateStatement(HqlParser.java:645)
	at org.hibernate.grammars.hql.HqlParser.statement(HqlParser.java:342)
	at org.hibernate.query.hql.internal.StandardHqlTranslator.parseHql(StandardHqlTranslator.java:143)
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:67)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.createHqlInterpretation(QueryInterpretationCacheStandardImpl.java:165)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.resolveHqlInterpretation(QueryInterpretationCacheStandardImpl.java:147)
	at org.hibernate.internal.AbstractSharedSessionContract.interpretHql(AbstractSharedSessionContract.java:744)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:794)
	... 92 common frames omitted
