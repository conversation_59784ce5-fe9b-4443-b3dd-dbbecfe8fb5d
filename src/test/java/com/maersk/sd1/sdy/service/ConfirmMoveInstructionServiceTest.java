package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdy.dto.ConfirmMoveInstructionInput;
import com.maersk.sd1.sdy.dto.ConfirmMoveInstructionOutput;
import com.maersk.sd1.sdy.dto.MovementInstructionDetailsDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ConfirmMoveInstructionServiceTest {

    @InjectMocks
    private ConfirmMoveInstructionService service;

    @Mock
    private MovementInstructionRepository movementInstructionRepository;
    @Mock
    private CatalogRepository catalogRepository;
    @Mock
    private ContainerRepository containerRepository;
    @Mock
    private CargoDocumentDetailRepository cargoDocumentDetailRepository;
    @Mock
    private ContainerLocationRepository containerLocationRepository;
    @Mock
    private EirRepository eirRepository;
    @Mock
    private UserRepository userRepository;

    private ConfirmMoveInstructionInput.Input validInput;
    private User testUser;
    private MovementInstruction testMovementInstruction;
    private MovementInstructionDetailsDto testDetails;
    private Map<String, Integer> testCatalogIds;

    @BeforeEach
    void setUp() {
        setupTestData();
    }

    private void setupTestData() {
        // Setup input
        validInput = new ConfirmMoveInstructionInput.Input();
        validInput.setMoveInstructionId(1);
        validInput.setUserAlias("testuser");

        // Setup user
        testUser = new User();
        testUser.setId(100);
        testUser.setAlias("testuser");
        testUser.setNames("Test User");

        // Setup movement instruction
        testMovementInstruction = new MovementInstruction();
        testMovementInstruction.setId(1);
        testMovementInstruction.setActive(true);
        
        Container container = new Container();
        container.setId(200);
        testMovementInstruction.setContainer(container);

        Block originBlock = new Block();
        originBlock.setId(1);
        originBlock.setCode("A1");
        testMovementInstruction.setOriginBlock(originBlock);

        Cell originCell = new Cell();
        originCell.setId(1);
        originCell.setColumn("1");
        originCell.setRow("1");
        testMovementInstruction.setOriginCell(originCell);

        Level originLevel = new Level();
        originLevel.setId(1);
        originLevel.setIndex(1);
        testMovementInstruction.setOriginLevel(originLevel);

        Block destBlock = new Block();
        destBlock.setId(2);
        destBlock.setCode("B1");
        Catalog blockType = new Catalog();
        blockType.setId(1001); // Stack type
        destBlock.setCatBlockType(blockType);
        testMovementInstruction.setDestinationBlock(destBlock);

        Cell destCell = new Cell();
        destCell.setId(2);
        destCell.setColumn("2");
        destCell.setRow("2");
        testMovementInstruction.setDestinationCell(destCell);

        Level destLevel = new Level();
        destLevel.setId(2);
        destLevel.setIndex(2);
        testMovementInstruction.setDestinationLevel(destLevel);

        // Setup movement instruction details
        testDetails = new MovementInstructionDetailsDto();
        testDetails.setCurrentStatusId(42898); // Executable status
        testDetails.setCurrentStatusCode("EXE");
        testDetails.setEirId(300);
        testDetails.setMovementTypeId(43080); // Non-gateout movement
        testDetails.setEirActive(true);
        testDetails.setEirTruckDepartureDate(null);
        testDetails.setEirCatEmptyFullId(43083); // Empty
        testDetails.setContainerId(200);
        testDetails.setEirCurrentContainerId(200);
        testDetails.setFatherMovementInstructionId(null);

        // Setup catalog IDs
        testCatalogIds = new HashMap<>();
        testCatalogIds.put("42900", 42900); // Cancelled
        testCatalogIds.put("42896", 42896); // Executed
        testCatalogIds.put("42899", 42899); // To be updated
        testCatalogIds.put("42898", 42898); // Executable
        testCatalogIds.put("42897", 42897); // Parent update
        testCatalogIds.put("moi_stt_to_be_upd", 42899);
        testCatalogIds.put("43081", 43081); // Gateout movement
        testCatalogIds.put("43084", 43084); // Full
        testCatalogIds.put("sd1_block_type_virtual", 1000);
        testCatalogIds.put("sd1_block_type_heap", 1002);
        testCatalogIds.put("sd1_block_type_stack", 1001);
    }

    @Test
    void confirmMoveInstruction_withValidInput_shouldReturnSuccess() {
        // Arrange
        when(userRepository.findByAlias("testuser")).thenReturn(Optional.of(testUser));
        when(catalogRepository.findIdsByAliases(anyList())).thenReturn(createCatalogResults());
        when(containerRepository.findIdByContainerNumber("NO-CNT")).thenReturn(999);
        when(containerRepository.findIdByContainerNumber("NOT APPLICA")).thenReturn(998);
        when(movementInstructionRepository.findMovementInstructionDetails(1)).thenReturn(testDetails);
        when(movementInstructionRepository.findById(1)).thenReturn(Optional.of(testMovementInstruction));
        when(movementInstructionRepository.save(any(MovementInstruction.class))).thenReturn(testMovementInstruction);
        when(movementInstructionRepository.getFinalResult(anyInt(), anyString(), anyString())).thenReturn(createSuccessOutput());

        // Act
        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);

        // Assert
        assertNotNull(result);
        assertEquals("SUCCESS", result.getRespEstado());
        assertEquals("Movement instruction executed successfully", result.getRespMensaje());
        verify(movementInstructionRepository).save(any(MovementInstruction.class));
        verify(containerLocationRepository).updateContainerLocation(anyInt(), anyInt(), anyInt(), anyInt(), any(), any(), any());
    }

    @Test
    void confirmMoveInstruction_withNullMoveInstructionId_shouldReturnError() {
        // Arrange
        validInput.setMoveInstructionId(null);

        // Act
        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);

        // Assert
        assertNotNull(result);
        assertEquals("BUSINESS_ERROR", result.getRespEstado());
        assertEquals("Move instruction ID cannot be null", result.getRespMensaje());
    }

    @Test
    void confirmMoveInstruction_withNullUserAlias_shouldReturnError() {
        // Arrange
        validInput.setUserAlias(null);

        // Act
        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);

        // Assert
        assertNotNull(result);
        assertEquals("BUSINESS_ERROR", result.getRespEstado());
        assertEquals("User alias cannot be null or empty", result.getRespMensaje());
    }

    @Test
    void confirmMoveInstruction_withUserNotFound_shouldReturnError() {
        // Arrange
        when(userRepository.findByAlias("testuser")).thenReturn(Optional.empty());

        // Act
        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);

        // Assert
        assertNotNull(result);
        assertEquals("BUSINESS_ERROR", result.getRespEstado());
        assertEquals("User not found with alias: testuser", result.getRespMensaje());
    }

    @Test
    void confirmMoveInstruction_withMovementInstructionNotFound_shouldReturnError() {
        // Arrange
        when(userRepository.findByAlias("testuser")).thenReturn(Optional.of(testUser));
        when(catalogRepository.findIdsByAliases(anyList())).thenReturn(createCatalogResults());
        when(containerRepository.findIdByContainerNumber("NO-CNT")).thenReturn(999);
        when(containerRepository.findIdByContainerNumber("NOT APPLICA")).thenReturn(998);
        when(movementInstructionRepository.findMovementInstructionDetails(1)).thenReturn(null);

        // Act
        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);

        // Assert
        assertNotNull(result);
        assertEquals("BUSINESS_ERROR", result.getRespEstado());
        assertEquals("Movement instruction not found with ID: 1", result.getRespMensaje());
    }

    @Test
    void confirmMoveInstruction_withLogicContainer_shouldReturnError() {
        // Arrange
        testDetails.setContainerId(999); // Dummy container ID
        
        when(userRepository.findByAlias("testuser")).thenReturn(Optional.of(testUser));
        when(catalogRepository.findIdsByAliases(anyList())).thenReturn(createCatalogResults());
        when(containerRepository.findIdByContainerNumber("NO-CNT")).thenReturn(999);
        when(containerRepository.findIdByContainerNumber("NOT APPLICA")).thenReturn(998);
        when(movementInstructionRepository.findMovementInstructionDetails(1)).thenReturn(testDetails);
        when(movementInstructionRepository.findById(1)).thenReturn(Optional.of(testMovementInstruction));
        when(movementInstructionRepository.save(any(MovementInstruction.class))).thenReturn(testMovementInstruction);

        // Act
        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);

        // Assert
        assertNotNull(result);
        assertEquals("BUSINESS_ERROR", result.getRespEstado());
        assertEquals("Cannot confirm movement instruction assigned to a logic container", result.getRespMensaje());
        verify(movementInstructionRepository).save(any(MovementInstruction.class)); // Should save cancellation
    }

    @Test
    void confirmMoveInstruction_withNonExecutableStatus_shouldReturnError() {
        // Arrange
        testDetails.setCurrentStatusId(42900); // Cancelled status
        testDetails.setCurrentStatusCode("CA");
        
        when(userRepository.findByAlias("testuser")).thenReturn(Optional.of(testUser));
        when(catalogRepository.findIdsByAliases(anyList())).thenReturn(createCatalogResults());
        when(containerRepository.findIdByContainerNumber("NO-CNT")).thenReturn(999);
        when(containerRepository.findIdByContainerNumber("NOT APPLICA")).thenReturn(998);
        when(movementInstructionRepository.findMovementInstructionDetails(1)).thenReturn(testDetails);
        when(movementInstructionRepository.findById(1)).thenReturn(Optional.of(testMovementInstruction));

        // Act
        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);

        // Assert
        assertNotNull(result);
        assertEquals("BUSINESS_ERROR", result.getRespEstado());
        assertTrue(result.getRespMensaje().contains("Movement instruction cannot be executed due to its current status"));
    }

    @Test
    void confirmMoveInstruction_withInactiveEir_shouldReturnError() {
        // Arrange
        testDetails.setEirActive(false);
        
        when(userRepository.findByAlias("testuser")).thenReturn(Optional.of(testUser));
        when(catalogRepository.findIdsByAliases(anyList())).thenReturn(createCatalogResults());
        when(containerRepository.findIdByContainerNumber("NO-CNT")).thenReturn(999);
        when(containerRepository.findIdByContainerNumber("NOT APPLICA")).thenReturn(998);
        when(movementInstructionRepository.findMovementInstructionDetails(1)).thenReturn(testDetails);
        when(movementInstructionRepository.findById(1)).thenReturn(Optional.of(testMovementInstruction));
        when(movementInstructionRepository.save(any(MovementInstruction.class))).thenReturn(testMovementInstruction);

        // Act
        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);

        // Assert
        assertNotNull(result);
        assertEquals("BUSINESS_ERROR", result.getRespEstado());
        assertTrue(result.getRespMensaje().contains("Movement instruction cannot be executed because its eir has been eliminated"));
        verify(movementInstructionRepository).save(any(MovementInstruction.class)); // Should save cancellation
    }

    @Test
    void confirmMoveInstruction_withGateoutMovementAndTruckDeparted_shouldReturnError() {
        // Arrange
        testDetails.setMovementTypeId(43081); // Gateout movement
        testDetails.setEirTruckDepartureDate(LocalDateTime.now().minusHours(1));
        
        when(userRepository.findByAlias("testuser")).thenReturn(Optional.of(testUser));
        when(catalogRepository.findIdsByAliases(anyList())).thenReturn(createCatalogResults());
        when(containerRepository.findIdByContainerNumber("NO-CNT")).thenReturn(999);
        when(containerRepository.findIdByContainerNumber("NOT APPLICA")).thenReturn(998);
        when(movementInstructionRepository.findMovementInstructionDetails(1)).thenReturn(testDetails);
        when(movementInstructionRepository.findById(1)).thenReturn(Optional.of(testMovementInstruction));
        when(movementInstructionRepository.save(any(MovementInstruction.class))).thenReturn(testMovementInstruction);

        // Act
        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);

        // Assert
        assertNotNull(result);
        assertEquals("BUSINESS_ERROR", result.getRespEstado());
        assertTrue(result.getRespMensaje().contains("Movement instruction cannot be executed because the truck of its eir has departed"));
        verify(movementInstructionRepository).save(any(MovementInstruction.class)); // Should save cancellation
    }

    @Test
    void confirmMoveInstruction_withException_shouldReturnInternalError() {
        // Arrange
        when(userRepository.findByAlias("testuser")).thenThrow(new RuntimeException("Database error"));

        // Act
        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);

        // Assert
        assertNotNull(result);
        assertEquals("BUSINESS_ERROR", result.getRespEstado());
        assertTrue(result.getRespMensaje().contains("Internal error"));
    }

    private List<Object[]> createCatalogResults() {
        List<Object[]> results = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : testCatalogIds.entrySet()) {
            results.add(new Object[]{entry.getKey(), entry.getValue()});
        }
        return results;
    }

    private ConfirmMoveInstructionOutput createSuccessOutput() {
        ConfirmMoveInstructionOutput output = new ConfirmMoveInstructionOutput();
        output.setInstruccionMovimientoId(1);
        output.setOrigenBloqueId(1);
        output.setOrigenBloque("A1");
        output.setOrigenColumna("1");
        output.setOrigenFila("1");
        output.setOrigenNivel(1);
        output.setDestinoBloqueId(2);
        output.setDestinoBloque("B1");
        output.setDestinoColumna("2");
        output.setDestinoFila("2");
        output.setDestinoNivel(2);
        return output;
    }
}
