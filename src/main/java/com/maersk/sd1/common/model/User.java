package com.maersk.sd1.common.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Data
@Table(name = "usuario", schema = "seg", indexes = {
        @Index(name = "idx_usuario_id", columnList = "id")
}, uniqueConstraints = {
        @UniqueConstraint(name = "U_SEG_USUARIO_ID", columnNames = {"id"})
})
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "usuario_id", nullable = false)
    private Integer id;

    @Size(max = 100)
    @Column(name = "correo", length = 100)
    private String mail;

    @Size(max = 100)
    @NotNull
    @Column(name = "clave", nullable = false, length = 100)
    private String key;

    @Column(name = "fecha_cambio_clave")
    private LocalDateTime dateChangeKey;

    @Size(max = 100)
    @NotNull
    @Column(name = "nombres", nullable = false, length = 100)
    private String names;

    @Size(max = 100)
    @NotNull
    @Column(name = "apellido_paterno", nullable = false, length = 100)
    private String firstLastName;

    @Size(max = 100)
    @Column(name = "apellido_materno", length = 100)
    private String secondLastName;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "empresa_id")
    private Company company;

    @NotNull
    @ColumnDefault("N'1'")
    @Column(name = "estado", nullable = false)
    private Character status;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "adjunto_foto_id")
    private Attachment attachmentPhoto;

    @Size(max = 100)
    @NotNull
    @ColumnDefault("''")
    @Column(name = "id", nullable = false, length = 100)
    private String alias;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "persona_id")
    private Person person;

    @Column(name = "contador_login")
    private Integer accountantLogin;

//    @Size(max = 2)
//    @Column(name = "correo_enviado_restaurar", length = 2)
//    private String restoreMailSent;

    public User(Integer userRegistrationId) {
        this.id = userRegistrationId;
    }

    public User(Integer id, String alias, String mail, LocalDateTime dateChangeKey, String names, String firstLastName, String secondLastName, Integer companyId, Character status) {
        this.id = id;
        this.alias = alias;
        this.mail = mail;
        this.dateChangeKey = dateChangeKey;
        this.names = names;
        this.firstLastName = firstLastName;
        this.secondLastName = secondLastName;
        this.company = new Company();
        this.company.setId(companyId);
        this.status = status;
    }
}