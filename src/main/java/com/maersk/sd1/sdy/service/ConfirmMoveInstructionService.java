package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdy.dto.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class ConfirmMoveInstructionService {

    private static final Logger logger = LogManager.getLogger(ConfirmMoveInstructionService.class);


    private final MovementInstructionRepository movementInstructionRepository;
    private final CatalogRepository catalogRepository;
    private final ContainerRepository containerRepository;
    private final CargoDocumentDetailRepository cargoDocumentDetailRepository;
    private final ContainerLocationRepository containerLocationRepository;
    private final EirRepository eirRepository;
    private final UserRepository userRepository;


    private static final String BUSINESS_ERROR = "BUSINESS_ERROR";

    @Transactional
    public ConfirmMoveInstructionOutput confirmMoveInstruction(ConfirmMoveInstructionInput.Input input) {
        log.info("Starting confirm move instruction for ID: {} by user: {}", 
                input.getMoveInstructionId(), input.getUserAlias());

        ConfirmMoveInstructionOutput output = new ConfirmMoveInstructionOutput();

        try {
            if (input.getMoveInstructionId() == null) {
                return buildErrorOutput("Move instruction ID cannot be null");
            }
            if (input.getUserAlias() == null || input.getUserAlias().trim().isEmpty()) {
                return buildErrorOutput("User alias cannot be null or empty");
            }

            User user = userRepository.findByUserAlias(input.getUserAlias())
                    .orElseThrow(() -> new IllegalArgumentException("User not found: " + input.getUserAlias()));

            Integer statusConfirmed = catalogRepository.findMovementId("EXE", "EMI");

            logger.info("Confirmed status ID: {}", statusConfirmed);

            MovementInstructionLocationDto details = movementInstructionRepository
                    .findLocationDtoByInstructionId(input.getMoveInstructionId());

            logger.info("Details: {}", details);
            if (details == null) {
                return buildErrorOutput("Movement instruction not found with ID: " + input.getMoveInstructionId());
            }

            Optional<MovementInstruction> moiOpt = movementInstructionRepository
                    .findById(input.getMoveInstructionId());
            if (moiOpt.isEmpty()) {
                return buildErrorOutput("Movement instruction entity not found");
            }
            MovementInstruction moi = moiOpt.get();

            if(details.getStatusMovementInstructionCode().equals("DP") || details.getStatusMovementInstructionCode().equals("IP") || details.getStatusMovementInstructionCode().equals("OH")) {

                moi.setCatStatus(new Catalog(statusConfirmed));
                moi.setModificationUser(user);
                moi.setModificationDate(LocalDateTime.now());
                movementInstructionRepository.save(moi);

                isOriginAndDestinationStack(details);
                isOriginStackAndDestinationVirtualOrHeap(details,user);
                isOriginVirtualOrHeapAndDestinationStack(details,user);
                isOriginVirtualOrHeapAndDestinationVirtualOrHeap(details,user);
            }

            else{
                output.setRespEstado(BUSINESS_ERROR);
                output.setRespMensaje("The selected movement instruction is in status "+ details.getStatusMovementInstructionDescription() +". You cannot confirm an instruction in status CREATED, TO BE ATTENDED, IN PROGRESS, or ON HOLD");
            }

        } catch (Exception e) {
            log.error("Error confirming movement instruction", e);
        }

        output = containerLocationRepository.findConfirmMoveInstructionOutput(input.getMoveInstructionId(), output.getRespEstado()!=null ? output.getRespEstado() : "SUCCESS", output.getRespMensaje()!=null ? output.getRespMensaje() : "Movement instruction confirmed successfully");

        return output;
    }

    private void isOriginAndDestinationStack(MovementInstructionLocationDto details) {
        if (details.getBlockOriginType().equals("STACK") && details.getBlockDestinationType().equals("STACK")) {
            removeContainerFromOrigin(details);
        }
    }

    private void isOriginStackAndDestinationVirtualOrHeap(MovementInstructionLocationDto details,User user) {
        if (details.getBlockOriginType().equals("STACK") && (details.getBlockDestinationType().equals("VIRTUAL") || details.getBlockDestinationType().equals("HEAP"))) {
            containerLocationRepository.clearContainerFromLocation(details.getBlockIdOrigin(), details.getCellIdOrigin(), details.getLevelIdOrigin());
            containerLocationRepository.clearContainerFromLocation(details.getBlock40IdOrigin(), details.getCell40IdOrigin(), details.getLevel40IdOrigin());

            if(!details.getBlockDestinationCode().equals("Out")){
                ContainerLocation containerLocation = new ContainerLocation();
                containerLocation.setBlock(Block.builder().id(details.getBlockIdDestination()).build());
                containerLocation.setCell(Cell.builder().id(details.getCellIdDestination()).build());
                containerLocation.setLevel(Level.builder().id(details.getLevelIdDestination()).build());
                containerLocation.setContainer(new Container(details.getContainerId()));
                containerLocation.setQuantityRemoved(0);
                containerLocation.setActive(true);
                containerLocation.setRegistrationUser(user);
                containerLocation.setRegistrationDate(LocalDateTime.now());
                containerLocationRepository.save(containerLocation);
            }
        }
    }

    private void isOriginVirtualOrHeapAndDestinationStack(MovementInstructionLocationDto details,User user) {
        if ((details.getBlockOriginType().equals("VIRTUAL") || details.getBlockOriginType().equals("HEAP")) && details.getBlockDestinationType().equals("STACK")) {
            containerLocationRepository.clearContainerFromLocation(details.getBlockIdOrigin(), details.getCellIdOrigin(), details.getLevelIdOrigin());
            containerLocationRepository.clearContainerFromLocation(details.getBlock40IdOrigin(), details.getCell40IdOrigin(), details.getLevel40IdOrigin());

            if(containerLocationRepository.countByBlockCellLevel(details.getBlockIdDestination(), details.getCellIdDestination(), details.getLevelIdDestination())>0){
                containerLocationRepository.updateContainerAtLocation(details.getBlockIdDestination(), details.getCellIdDestination(), details.getLevelIdDestination(), details.getContainerId());
            }
            else{
                ContainerLocation containerLocation = new ContainerLocation();
                containerLocation.setBlock(Block.builder().id(details.getBlockIdDestination()).build());
                containerLocation.setCell(Cell.builder().id(details.getCellIdDestination()).build());
                containerLocation.setLevel(Level.builder().id(details.getLevelIdDestination()).build());
                containerLocation.setContainer(new Container(details.getContainerId()));
                containerLocation.setQuantityRemoved(0);
                containerLocation.setActive(true);
                containerLocation.setRegistrationUser(user);
                containerLocation.setRegistrationDate(LocalDateTime.now());
                containerLocationRepository.save(containerLocation);
            }

            if(details.getBlock40IdDestination() != null){
                if(containerLocationRepository.countByBlockCellLevel(details.getBlock40IdDestination(), details.getCelda40IdDestino(), details.getNivel40IdDestino())>0){
                    containerLocationRepository.updateContainerAtLocation(details.getBlock40IdDestination(), details.getCelda40IdDestino(), details.getNivel40IdDestino(), details.getContainerId());
                }
                else{
                    ContainerLocation containerLocation = new ContainerLocation();
                    containerLocation.setBlock(Block.builder().id(details.getBlock40IdDestination()).build());
                    containerLocation.setCell(Cell.builder().id(details.getCelda40IdDestino()).build());
                    containerLocation.setLevel(Level.builder().id(details.getNivel40IdDestino()).build());
                    containerLocation.setContainer(new Container(details.getContainerId()));
                    containerLocation.setQuantityRemoved(0);
                    containerLocation.setActive(true);
                    containerLocation.setRegistrationUser(user);
                    containerLocation.setRegistrationDate(LocalDateTime.now());
                    containerLocationRepository.save(containerLocation);
                }
            }
        }
    }

    private void isOriginVirtualOrHeapAndDestinationVirtualOrHeap(MovementInstructionLocationDto details,User user) {
        if ((details.getBlockOriginType().equals("VIRTUAL") || details.getBlockOriginType().equals("HEAP")) && (details.getBlockDestinationType().equals("VIRTUAL") || details.getBlockDestinationType().equals("HEAP"))) {
            containerLocationRepository.deleteByContainerIdAndBlockId(details.getContainerId(), details.getBlockIdOrigin());

            ContainerLocation containerLocation = new ContainerLocation();
            containerLocation.setBlock(Block.builder().id(details.getBlockIdDestination()).build());
            containerLocation.setCell(Cell.builder().id(details.getCellIdDestination()).build());
            containerLocation.setLevel(Level.builder().id(details.getLevelIdDestination()).build());
            containerLocation.setContainer(new Container(details.getContainerId()));
            containerLocation.setQuantityRemoved(0);
            containerLocation.setActive(true);
            containerLocation.setRegistrationUser(user);
            containerLocation.setRegistrationDate(LocalDateTime.now());
            containerLocationRepository.save(containerLocation);

            if(details.getBlock40IdDestination() != null){
                ContainerLocation containerLocation40 = new ContainerLocation();
                containerLocation40.setBlock(Block.builder().id(details.getBlock40IdDestination()).build());
                containerLocation40.setCell(Cell.builder().id(details.getCelda40IdDestino()).build());
                containerLocation40.setLevel(Level.builder().id(details.getNivel40IdDestino()).build());
                containerLocation40.setContainer(new Container(details.getContainerId()));
                containerLocation40.setQuantityRemoved(0);
                containerLocation40.setActive(true);
                containerLocation40.setRegistrationUser(user);
                containerLocation40.setRegistrationDate(LocalDateTime.now());
                containerLocationRepository.save(containerLocation40);
            }
        }
    }

    private void removeContainerFromOrigin(MovementInstructionLocationDto details) {

        Integer o20BlockId = details.getBlockIdOrigin() != null ? details.getBlockIdOrigin() : null;
        Integer o20CellId = details.getCellIdOrigin() != null ? details.getCellIdOrigin() : null;
        Integer o20LevelId = details.getLevelIdOrigin() != null ? details.getLevelIdOrigin() : null;
        Integer o40BlockId = details.getBlock40IdOrigin() != null ? details.getBlock40IdOrigin() : null;
        Integer o40CellId = details.getCell40IdOrigin() != null ? details.getCell40IdOrigin() : null;
        Integer o40LevelId = details.getLevel40IdOrigin() != null ? details.getLevel40IdOrigin() : null;

        Integer d20BlockId = details.getBlockIdDestination() != null ? details.getBlockIdDestination() : null;
        Integer d20CellId = details.getCellIdDestination() != null ? details.getCellIdDestination() : null;
        Integer d20LevelId = details.getLevelIdDestination() != null ? details.getLevelIdDestination() : null;
        Integer d40BlockId = details.getBlock40IdDestination() != null ? details.getBlock40IdDestination() : null;
        Integer d40CellId = details.getCelda40IdDestino() != null ? details.getCelda40IdDestino() : null;
        Integer d40LevelId = details.getNivel40IdDestino() != null ? details.getNivel40IdDestino() : null;

        containerLocationRepository.clearContainerFromLocation(o20BlockId, o20CellId, o20LevelId);
        containerLocationRepository.clearContainerFromLocation(o40BlockId, o40CellId, o40LevelId);
        containerLocationRepository.updateContainerAtLocation(d20BlockId, d20CellId, d20LevelId, details.getContainerId());
        containerLocationRepository.updateContainerAtLocation(d40BlockId, d40CellId, d40LevelId, details.getContainerId());
    }

    private ConfirmMoveInstructionOutput buildErrorOutput(String message) {
        ConfirmMoveInstructionOutput output = new ConfirmMoveInstructionOutput();
        output.setRespEstado(BUSINESS_ERROR);
        output.setRespMensaje(message);
        return output;
    }
}
